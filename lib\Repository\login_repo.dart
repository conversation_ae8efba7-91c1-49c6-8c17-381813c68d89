import 'dart:convert';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../services/firebase_database_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:salespro_admin/Repository/profile_details_repo.dart';
import 'package:salespro_admin/Screen/Home/home_screen.dart';

import '../Screen/Authentication/add_profile.dart';
import '../const.dart';
import '../model/user_role_model.dart';

final logInProvider = ChangeNotifierProvider((ref) => LogInRepo());

class LogInRepo extends ChangeNotifier {
  String email = '';
  String password = '';

  Future<void> signIn(BuildContext context) async {
    EasyLoading.show(status: 'Login...');
    try {
      mainLoginEmail = email;
      mainLoginPassword = password;
      await FirebaseAuth.instance
          .signInWithEmailAndPassword(email: email, password: password)
          .then((value) async {
            if (await checkSubUser()) {
              EasyLoading.showSuccess('Successful');
              setUserDataOnLocalData(
                uid: constUserId,
                subUserTitle: constSubUserTitle,
                isSubUser: true,
              );
              putUserDataImidiyate(uid: constUserId, title: '', isSubUse: true);
              Navigator.of(context).pushNamed(MtHomeScreen.route);
            } else {
              EasyLoading.showSuccess('Successful');
              await setUserDataOnLocalData(
                uid: FirebaseAuth.instance.currentUser!.uid,
                subUserTitle: '',
                isSubUser: false,
              );
              putUserDataImidiyate(
                uid: FirebaseAuth.instance.currentUser!.uid,
                title: '',
                isSubUse: false,
              );

              if (await ProfileRepo().isProfileSetupDone()) {
                Navigator.of(context).pushNamed(MtHomeScreen.route);
              } else {
                const ProfileAdd().launch(context);
              }
            }
          });
    } on FirebaseAuthException catch (e) {
      EasyLoading.showError(e.message.toString());
      if (e.code == 'user-not-found') {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No user found for that email.'),
            duration: Duration(seconds: 3),
          ),
        );
      } else if (e.code == 'wrong-password') {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Wrong password provided for that user.'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      EasyLoading.showError(e.toString());
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(e.toString()),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  Future<bool> checkSubUser() async {
    debugPrint('🔍 بدء التحقق من المستخدمين الفرعيين للإيميل: $email');

    final SharedPreferences prefs = await SharedPreferences.getInstance();
    bool isSubUser = false;

    try {
      await FirebaseDatabaseService.ref('Admin Panel')
          .child('User Role')
          .orderByKey()
          .get()
          .then((value) {
            debugPrint('📊 تم الحصول على بيانات User Role من Firebase');
            if (!value.exists) {
              debugPrint('❌ لا توجد بيانات في Admin Panel/User Role');
              return;
            }
            debugPrint('📋 عدد المستخدمين الفرعيين: ${value.children.length}');

            for (var element in value.children) {
              try {
                var data = UserRoleModel.fromJson(
                  jsonDecode(jsonEncode(element.value)),
                );
                debugPrint('🔍 فحص المستخدم: ${data.email}');

                if (data.email == email) {
                  debugPrint('✅ تم العثور على المستخدم الفرعي: ${data.email}');
                  debugPrint('🔍 صلاحية المبيعات: ${data.salePermission}');
                  debugPrint('🔍 صلاحية الأطراف: ${data.partiesPermission}');
                  debugPrint('🔍 صلاحية التقارير: ${data.reportsPermission}');

                  // حفظ بيانات الصلاحيات في SharedPreferences
                  prefs.setString('userPermission', json.encode(data));
                  prefs.setString('subUserEmail', email);
                  prefs.setBool('isSubUser', true);
                  prefs.setString('userId', data.databaseId);
                  prefs.setString('subUserTitle', data.userTitle);

                  debugPrint('💾 تم حفظ بيانات الصلاحيات في SharedPreferences');

                  // تحديث المتغيرات العامة
                  finalUserRoleModel = data;
                  constUserId = data.databaseId;
                  constSubUserTitle = data.userTitle;
                  isSubUser = true;
                  return;
                }
              } catch (e) {
                debugPrint('❌ خطأ في معالجة بيانات المستخدم: $e');
                continue;
              }
            }
          });

      if (!isSubUser) {
        debugPrint('ℹ️ لم يتم العثور على مستخدم فرعي - مستخدم رئيسي');
        await prefs.setBool('isSubUser', false);
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من المستخدمين الفرعيين: $e');
    }

    debugPrint('🔄 انتهاء التحقق - النتيجة: $isSubUser');
    return isSubUser;
  }
}

Future<void> sendEmailVerification() async {
  User? user = FirebaseAuth.instance.currentUser;

  try {
    await user?.sendEmailVerification();
    print('Email verification link sent');
  } catch (e) {
    print('Error sending email verification link: $e');
  }
}
